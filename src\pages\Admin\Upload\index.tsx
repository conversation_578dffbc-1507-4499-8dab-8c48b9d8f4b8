import {
  historicalElementData,
  mountainData,
  photoData,
  waterSystemData,
} from '@/services/mockData';
import { uploadFile, uploadFiles, uploadUtils } from '@/services/upload';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  InboxOutlined,
  LoadingOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Form,
  Image,
  Input,
  message,
  Modal,
  Popconfirm,
  Progress,
  Select,
  Space,
  Table,
  Typography,
  Upload,
} from 'antd';
import type { UploadFile, UploadProps } from 'antd';
import React, { useState, useEffect } from 'react';
import './index.less';

// 开发环境下导入测试工具
if (process.env.NODE_ENV === 'development') {
  import('@/utils/uploadTest');
}

const { Title } = Typography;
const { Dragger } = Upload;

// 定义 PhotoData 的类型
type PhotoData = (typeof photoData)[number];

const AdminUpload: React.FC = () => {
  const [data, setData] = useState(photoData);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const handleEdit = (record: any) => {
    setEditingItem(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleDelete = (id: number) => {
    setData(data.filter((item) => item.id !== id));
    message.success('删除成功！');
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (editingItem) {
        // 编辑
        setData(
          data.map((item) =>
            item.id === editingItem.id ? { ...item, ...values } : item,
          ),
        );
        message.success('编辑成功！');
      }

      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '预览',
      dataIndex: 'url',
      key: 'preview',
      width: 100,
      render: (url: string) => (
        <Image
          width={60}
          height={40}
          src={url}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          preview={{
            mask: <EyeOutlined />,
          }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '文件路径',
      dataIndex: 'url',
      key: 'url',
      ellipsis: true,
      render: (url: string) => (
        <a href={url} target="_blank" rel="noopener noreferrer">
          {url}
        </a>
      ),
    },
    {
      title: '文件大小',
      key: 'size',
      width: 100,
      render: (_: any, record: any) => {
        // 这里可以从实际的文件信息中获取大小，暂时显示占位符
        return '未知';
      },
    },
    {
      title: '关联对象',
      key: 'related',
      render: (_: any, record: any) => {
        if (record.mountain_id) {
          const mountain = mountainData.find(
            (m) => m.id === record.mountain_id,
          );
          return `山塬: ${mountain?.name || '未知'}`;
        }
        if (record.water_system_id) {
          const waterSystem = waterSystemData.find(
            (w) => w.id === record.water_system_id,
          );
          return `水系: ${waterSystem?.name || '未知'}`;
        }
        if (record.historical_element_id) {
          const element = historicalElementData.find(
            (h) => h.id === record.historical_element_id,
          );
          return `历史要素: ${element?.name || '未知'}`;
        }
        return '无关联';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个文件吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 自定义上传函数
  const customUpload = async (options: any) => {
    const { file, onSuccess, onError, onProgress } = options;

    // 验证文件
    const validation = uploadUtils.validateFile(file);
    if (!validation.valid) {
      message.error(validation.message);
      onError(new Error(validation.message));
      return;
    }

    try {
      setUploading(true);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const current = prev[file.uid] || 0;
          const next = Math.min(current + Math.random() * 30, 90);
          onProgress({ percent: next });
          return { ...prev, [file.uid]: next };
        });
      }, 200);

      // 调用上传接口
      const response = await uploadFile(file);

      clearInterval(progressInterval);

      if (response.errCode === 0 && response.data) {
        // 上传成功
        setUploadProgress(prev => ({ ...prev, [file.uid]: 100 }));
        onProgress({ percent: 100 });
        onSuccess(response.data);

        message.success(`${file.name} 上传成功`);

        // 添加到数据列表
        const newFile = {
          id: Date.now(),
          name: response.data.filename,
          url: response.data.url,
          mountain_id: null,
          water_system_id: null,
          historical_element_id: null,
        } as unknown as PhotoData;
        setData(prev => [...prev, newFile]);

        // 清除进度
        setTimeout(() => {
          setUploadProgress(prev => {
            const newProgress = { ...prev };
            delete newProgress[file.uid];
            return newProgress;
          });
        }, 1000);
      } else {
        throw new Error(response.msg || '上传失败');
      }
    } catch (error: any) {
      console.error('上传失败:', error);
      message.error(error.message || `${file.name} 上传失败`);
      onError(error);
    } finally {
      setUploading(false);
    }
  };

  // 批量上传函数
  const handleBatchUpload = async (files: File[]) => {
    if (files.length === 0) return;

    // 验证所有文件
    for (const file of files) {
      const validation = uploadUtils.validateFile(file);
      if (!validation.valid) {
        message.error(`文件 ${file.name} ${validation.message}`);
        return;
      }
    }

    try {
      setUploading(true);
      message.loading('正在批量上传文件...', 0);

      const response = await uploadFiles(files);

      if (response.errCode === 0 && response.data) {
        message.destroy();
        message.success(`成功上传 ${response.data.length} 个文件`);

        // 添加到数据列表
        const newFiles = response.data.map((fileData, index) => ({
          id: Date.now() + index,
          name: fileData.filename,
          url: fileData.url,
          mountain_id: null,
          water_system_id: null,
          historical_element_id: null,
        })) as unknown as PhotoData[];

        setData(prev => [...prev, ...newFiles]);
        setFileList([]);
      } else {
        throw new Error(response.msg || '批量上传失败');
      }
    } catch (error: any) {
      message.destroy();
      console.error('批量上传失败:', error);
      message.error(error.message || '批量上传失败');
    } finally {
      setUploading(false);
    }
  };

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    customRequest: customUpload,
    fileList,
    onChange: ({ fileList: newFileList }) => {
      setFileList(newFileList);
    },
    onRemove: (file) => {
      setFileList(prev => prev.filter(item => item.uid !== file.uid));
    },
    beforeUpload: (file) => {
      const validation = uploadUtils.validateFile(file);
      if (!validation.valid) {
        message.error(validation.message);
        return false;
      }
      return true;
    },
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
      showDownloadIcon: false,
    },
  };

  return (
    <div className="upload-container" style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        资源管理
      </Title>

      {/* 统计信息 */}
      <div className="stats-cards" style={{ marginBottom: 24 }}>
        <Space size="large" wrap>
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div className="stat-number" style={{ color: '#1890ff' }}>
                {data.length}
              </div>
              <div className="stat-label">总文件数</div>
            </div>
          </Card>
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div className="stat-number" style={{ color: '#52c41a' }}>
                {data.filter(item => item.mountain_id).length}
              </div>
              <div className="stat-label">关联山塬</div>
            </div>
          </Card>
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div className="stat-number" style={{ color: '#722ed1' }}>
                {data.filter(item => item.water_system_id).length}
              </div>
              <div className="stat-label">关联水系</div>
            </div>
          </Card>
          <Card size="small" style={{ minWidth: 120 }}>
            <div style={{ textAlign: 'center' }}>
              <div className="stat-number" style={{ color: '#fa8c16' }}>
                {data.filter(item => item.historical_element_id).length}
              </div>
              <div className="stat-label">关联历史要素</div>
            </div>
          </Card>
        </Space>
      </div>

      {/* 上传区域 */}
      <Card
        title="文件上传"
        style={{ marginBottom: 24 }}
        extra={
          fileList.length > 0 && (
            <Button
              type="primary"
              loading={uploading}
              onClick={() => handleBatchUpload(fileList.map(f => f.originFileObj as File))}
              disabled={fileList.some(f => !f.originFileObj)}
            >
              批量上传 ({fileList.length})
            </Button>
          )
        }
      >
        <Dragger {...uploadProps} style={{ padding: '20px' }}>
          <p className="ant-upload-drag-icon">
            {uploading ? <LoadingOutlined /> : <InboxOutlined />}
          </p>
          <p className="ant-upload-text">
            {uploading ? '正在上传...' : '点击或拖拽文件到此区域上传'}
          </p>
          <p className="ant-upload-hint">
            支持单个或批量上传。仅支持图片格式：JPG、JPEG、PNG、GIF、BMP、WEBP
            <br />
            单个文件最大 50MB
          </p>
        </Dragger>

        {/* 上传进度显示 */}
        {Object.keys(uploadProgress).length > 0 && (
          <div className="upload-progress">
            {Object.entries(uploadProgress).map(([uid, progress]) => {
              const file = fileList.find(f => f.uid === uid);
              return file ? (
                <div key={uid} className="progress-item">
                  <div className="file-info">
                    <span className="file-name">{file.name}</span>
                    <span className="file-size">
                      {uploadUtils.formatFileSize(file.size || 0)}
                    </span>
                  </div>
                  <Progress percent={Math.round(progress)} size="small" />
                </div>
              ) : null;
            })}
          </div>
        )}

        {/* 上传提示 */}
        <div className="upload-tips">
          <div className="tips-title">📋 上传说明</div>
          <ul className="tips-list">
            <li>支持的图片格式：JPG、JPEG、PNG、GIF、BMP、WEBP</li>
            <li>单个文件最大大小：50MB</li>
            <li>支持拖拽上传和批量上传</li>
            <li>上传成功后可以编辑文件信息并关联到相关对象</li>
            <li>文件将自动按日期分类存储</li>
          </ul>
        </div>
      </Card>

      {/* 文件列表 */}
      <Card
        title="文件列表"
        extra={
          <Space>
            <Button
              type="default"
              onClick={() => setData(photoData)}
              size="small"
            >
              重置数据
            </Button>
          </Space>
        }
      >
        <Table
          className="file-table"
          columns={columns}
          dataSource={data}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 个文件`,
          }}
          scroll={{ x: 800 }}
          size="middle"
        />
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title="编辑文件信息"
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="文件名称"
            rules={[{ required: true, message: '请输入文件名称' }]}
          >
            <Input placeholder="请输入文件名称" />
          </Form.Item>

          <Form.Item name="mountain_id" label="关联山塬">
            <Select placeholder="请选择关联的山塬" allowClear>
              {mountainData.map((mountain) => (
                <Select.Option key={mountain.id} value={mountain.id}>
                  {mountain.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="water_system_id" label="关联水系">
            <Select placeholder="请选择关联的水系" allowClear>
              {waterSystemData.map((waterSystem) => (
                <Select.Option key={waterSystem.id} value={waterSystem.id}>
                  {waterSystem.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="historical_element_id" label="关联历史要素">
            <Select placeholder="请选择关联的历史要素" allowClear>
              {historicalElementData.map((element) => (
                <Select.Option key={element.id} value={element.id}>
                  {element.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminUpload;
