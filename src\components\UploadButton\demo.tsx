import { Card, Space, Typography } from 'antd';
import React, { useState } from 'react';
import UploadButton from './index';

const { Title, Paragraph, Text } = Typography;

/**
 * UploadButton 组件使用示例
 */
const UploadButtonDemo: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  const handleUploadSuccess = (fileInfo: any) => {
    console.log('上传成功:', fileInfo);
    setUploadedFiles(prev => [...prev, fileInfo]);
  };

  const handleUploadError = (error: any) => {
    console.error('上传失败:', error);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>UploadButton 组件示例</Title>
      
      <Card title="基础用法" style={{ marginBottom: 16 }}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <Paragraph>
            <Text strong>默认上传按钮：</Text>
          </Paragraph>
          <UploadButton
            onSuccess={handleUploadSuccess}
            onError={handleUploadError}
          />
        </Space>
      </Card>

      <Card title="不同样式" style={{ marginBottom: 16 }}>
        <Space wrap>
          <UploadButton
            buttonType="primary"
            buttonText="主要按钮"
            onSuccess={handleUploadSuccess}
            onError={handleUploadError}
          />
          <UploadButton
            buttonType="dashed"
            buttonText="虚线按钮"
            onSuccess={handleUploadSuccess}
            onError={handleUploadError}
          />
          <UploadButton
            buttonType="link"
            buttonText="链接按钮"
            onSuccess={handleUploadSuccess}
            onError={handleUploadError}
          />
        </Space>
      </Card>

      <Card title="多文件上传" style={{ marginBottom: 16 }}>
        <UploadButton
          multiple
          buttonText="选择多个文件"
          onSuccess={handleUploadSuccess}
          onError={handleUploadError}
        />
      </Card>

      <Card title="自定义限制" style={{ marginBottom: 16 }}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <Paragraph>
            <Text strong>限制文件大小为 5MB：</Text>
          </Paragraph>
          <UploadButton
            maxSize={5 * 1024 * 1024} // 5MB
            buttonText="上传（最大5MB）"
            onSuccess={handleUploadSuccess}
            onError={handleUploadError}
          />
        </Space>
      </Card>

      <Card title="隐藏文件列表" style={{ marginBottom: 16 }}>
        <UploadButton
          showUploadList={false}
          buttonText="上传（无列表）"
          onSuccess={handleUploadSuccess}
          onError={handleUploadError}
        />
      </Card>

      {uploadedFiles.length > 0 && (
        <Card title="上传成功的文件">
          <Space direction="vertical" style={{ width: '100%' }}>
            {uploadedFiles.map((file, index) => (
              <div key={index} style={{ padding: '8px', background: '#f5f5f5', borderRadius: '4px' }}>
                <div><Text strong>文件名:</Text> {file.filename}</div>
                <div><Text strong>大小:</Text> {(file.size / 1024).toFixed(2)} KB</div>
                <div><Text strong>URL:</Text> <a href={file.url} target="_blank" rel="noopener noreferrer">{file.url}</a></div>
              </div>
            ))}
          </Space>
        </Card>
      )}
    </div>
  );
};

export default UploadButtonDemo;
