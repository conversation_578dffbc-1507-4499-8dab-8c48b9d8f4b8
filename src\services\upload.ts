import { request } from '@umijs/max';

// 上传相关的API接口

/**
 * 上传文件响应类型
 */
export interface UploadFileResponse {
  url: string;
  filename: string;
  size: number;
}

/**
 * 上传单个文件
 */
export async function uploadFile(file: File): Promise<API.ResType<UploadFileResponse>> {
  const formData = new FormData();
  formData.append('file', file);

  return request('/upload/file', {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 上传多个文件
 */
export async function uploadFiles(files: File[]): Promise<API.ResType<UploadFileResponse[]>> {
  const formData = new FormData();
  files.forEach(file => {
    formData.append('files', file);
  });

  return request('/upload/files', {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 文件上传工具函数
 */
export const uploadUtils = {
  /**
   * 检查文件类型是否支持
   */
  isImageFile: (file: File): boolean => {
    const supportedTypes = [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp'
    ];
    return supportedTypes.includes(file.type);
  },

  /**
   * 检查文件大小是否符合要求
   */
  isValidSize: (file: File, maxSize: number = 50 * 1024 * 1024): boolean => {
    return file.size <= maxSize;
  },

  /**
   * 格式化文件大小
   */
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * 获取文件扩展名
   */
  getFileExtension: (filename: string): string => {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
  },

  /**
   * 验证文件
   */
  validateFile: (file: File): { valid: boolean; message?: string } => {
    if (!uploadUtils.isImageFile(file)) {
      return {
        valid: false,
        message: '不支持的文件类型，仅支持图片文件（.jpg, .jpeg, .png, .gif, .bmp, .webp）'
      };
    }

    if (!uploadUtils.isValidSize(file)) {
      return {
        valid: false,
        message: '文件大小超过限制（50MB）'
      };
    }

    return { valid: true };
  }
};
