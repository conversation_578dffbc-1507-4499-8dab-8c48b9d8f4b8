// 上传功能测试工具
import { uploadUtils } from '@/services/upload';

/**
 * 创建测试文件
 */
export const createTestFile = (
  name: string = 'test.jpg',
  size: number = 1024 * 1024, // 1MB
  type: string = 'image/jpeg'
): File => {
  const content = new Array(size).fill(0).map(() => Math.random().toString(36)).join('');
  const blob = new Blob([content], { type });
  return new File([blob], name, { type });
};

/**
 * 测试文件验证功能
 */
export const testFileValidation = () => {
  console.group('🧪 文件验证测试');
  
  // 测试有效的图片文件
  const validFile = createTestFile('test.jpg', 1024 * 1024, 'image/jpeg');
  const validResult = uploadUtils.validateFile(validFile);
  console.log('✅ 有效文件测试:', validResult);
  
  // 测试无效的文件类型
  const invalidTypeFile = createTestFile('test.txt', 1024, 'text/plain');
  const invalidTypeResult = uploadUtils.validateFile(invalidTypeFile);
  console.log('❌ 无效类型测试:', invalidTypeResult);
  
  // 测试文件过大
  const oversizeFile = createTestFile('large.jpg', 60 * 1024 * 1024, 'image/jpeg');
  const oversizeResult = uploadUtils.validateFile(oversizeFile);
  console.log('❌ 文件过大测试:', oversizeResult);
  
  console.groupEnd();
};

/**
 * 测试文件大小格式化
 */
export const testFileSizeFormat = () => {
  console.group('📏 文件大小格式化测试');
  
  const testSizes = [
    0,
    1024,
    1024 * 1024,
    1024 * 1024 * 1024,
    50 * 1024 * 1024
  ];
  
  testSizes.forEach(size => {
    const formatted = uploadUtils.formatFileSize(size);
    console.log(`${size} bytes = ${formatted}`);
  });
  
  console.groupEnd();
};

/**
 * 测试文件扩展名获取
 */
export const testFileExtension = () => {
  console.group('📄 文件扩展名测试');
  
  const testFiles = [
    'image.jpg',
    'photo.jpeg',
    'picture.png',
    'animation.gif',
    'document.pdf',
    'file.with.multiple.dots.txt',
    'noextension'
  ];
  
  testFiles.forEach(filename => {
    const ext = uploadUtils.getFileExtension(filename);
    console.log(`${filename} -> .${ext}`);
  });
  
  console.groupEnd();
};

/**
 * 运行所有测试
 */
export const runAllTests = () => {
  console.log('🚀 开始运行上传工具测试...');
  testFileValidation();
  testFileSizeFormat();
  testFileExtension();
  console.log('✨ 所有测试完成！');
};

// 在开发环境下自动运行测试
if (process.env.NODE_ENV === 'development') {
  // 延迟执行，避免影响应用启动
  setTimeout(() => {
    if (window.location.pathname.includes('/admin/upload')) {
      runAllTests();
    }
  }, 2000);
}
